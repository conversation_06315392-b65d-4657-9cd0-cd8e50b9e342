<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول - خبرة المطابخ</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            max-width: 500px;
            width: 100%;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 2rem;
            margin: 0;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .credentials {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }
        .cred-item {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 1.1rem;
        }
        .test-form {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 1rem;
            box-sizing: border-box;
        }
        .btn {
            background: #4CAF50;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            width: 100%;
            margin: 10px 0;
            transition: background 0.3s ease;
        }
        .btn:hover {
            background: #45a049;
        }
        .btn.secondary {
            background: #2196F3;
        }
        .btn.secondary:hover {
            background: #1976D2;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
        }
        .success {
            background: rgba(76, 175, 80, 0.3);
            border: 1px solid #4CAF50;
        }
        .error {
            background: rgba(244, 67, 54, 0.3);
            border: 1px solid #f44336;
        }
        .links {
            text-align: center;
            margin-top: 30px;
        }
        .links a {
            color: #FFD700;
            text-decoration: none;
            margin: 0 15px;
            font-weight: bold;
        }
        .links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 اختبار تسجيل الدخول</h1>
            <p>خبرة المطابخ - لوحة التحكم</p>
        </div>

        <div class="credentials">
            <h3>📋 بيانات الدخول الصحيحة:</h3>
            <div class="cred-item">
                <strong>اسم المستخدم:</strong> admin
            </div>
            <div class="cred-item">
                <strong>كلمة المرور:</strong> admin123
            </div>
            <div class="cred-item">
                <strong>البريد الإلكتروني:</strong> <EMAIL>
            </div>
        </div>

        <div class="test-form">
            <h3>🧪 اختبار تسجيل الدخول:</h3>
            <form id="loginForm">
                <div class="form-group">
                    <label for="username">اسم المستخدم أو البريد الإلكتروني:</label>
                    <input type="text" id="username" name="username" value="admin" required>
                </div>
                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <input type="password" id="password" name="password" value="admin123" required>
                </div>
                <button type="submit" class="btn">اختبار تسجيل الدخول</button>
            </form>
            <div id="result"></div>
        </div>

        <div class="links">
            <a href="https://khobrakitchens.com/admin" target="_blank">🔗 لوحة التحكم الفعلية</a>
            <a href="https://khobrakitchens.com" target="_blank">🏠 الموقع الرئيسي</a>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            // Clear previous result
            resultDiv.innerHTML = '';
            
            // Test credentials
            const correctUsername = 'admin';
            const correctEmail = '<EMAIL>';
            const correctPassword = 'admin123';
            
            if ((username === correctUsername || username === correctEmail) && password === correctPassword) {
                resultDiv.innerHTML = '<div class="result success">✅ نجح! بيانات الدخول صحيحة</div>';
                
                // Test actual login to the real site
                setTimeout(() => {
                    resultDiv.innerHTML += '<div class="result success">🔄 جاري اختبار الموقع الفعلي...</div>';
                    window.open('https://khobrakitchens.com/admin', '_blank');
                }, 1000);
            } else {
                resultDiv.innerHTML = '<div class="result error">❌ فشل! بيانات الدخول غير صحيحة</div>';
                resultDiv.innerHTML += '<div class="result error">المدخل: ' + username + ' / ' + password + '</div>';
                resultDiv.innerHTML += '<div class="result error">المطلوب: admin / admin123</div>';
            }
        });

        // Auto-fill correct credentials
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').value = 'admin';
            document.getElementById('password').value = 'admin123';
        });
    </script>
</body>
</html>
