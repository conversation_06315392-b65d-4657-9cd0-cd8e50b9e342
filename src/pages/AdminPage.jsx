import { useEffect, useState } from 'react'
import Login from '../admin/components/Login'
import Dashboard from '../admin/components/Dashboard'
import { AuthProvider, useAuth } from '../admin/context/AuthContext'
import { DataProvider } from '../admin/context/DataContext'
import { initializeDatabase } from '../admin/utils/initDatabase'

function AdminContent() {
  const { isAuthenticated, loading } = useAuth()
  const [dbInitialized, setDbInitialized] = useState(false)
  const [dbLoading, setDbLoading] = useState(true)

  useEffect(() => {
    const initDB = async () => {
      try {
        setDbLoading(true)
        await initializeDatabase()
        setDbInitialized(true)
      } catch (error) {
        console.error('Failed to initialize database:', error)
        setDbInitialized(false)
      } finally {
        setDbLoading(false)
      }
    }

    initDB()
  }, [])

  if (loading || dbLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-6"></div>
          <p className="text-gray-600 text-xl font-bold">
            {dbLoading ? 'جاري تهيئة قاعدة البيانات...' : 'جاري التحميل...'}
          </p>
        </div>
      </div>
    )
  }

  if (!dbInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <i className="ri-error-warning-line text-2xl text-red-500"></i>
          </div>
          <p className="text-red-600 text-xl font-bold mb-4">فشل في تهيئة قاعدة البيانات</p>
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            إعادة المحاولة
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30" dir="rtl">
      {isAuthenticated ? (
        <DataProvider>
          <Dashboard />
        </DataProvider>
      ) : (
        <Login />
      )}
    </div>
  )
}

function AdminPage() {
  return (
    <AuthProvider>
      <AdminContent />
    </AuthProvider>
  )
}

export default AdminPage
