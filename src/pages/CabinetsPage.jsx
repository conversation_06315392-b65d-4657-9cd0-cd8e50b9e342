import { useState, useEffect } from 'react'
import { Helmet } from 'react-helmet-async'
import ProductModal from '../components/ProductModal'

const CabinetsPage = () => {
  const [cabinets, setCabinets] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedProduct, setSelectedProduct] = useState(null)
  const [currentCategory, setCurrentCategory] = useState('cabinet')

  // الكلمات المفتاحية للخزانات
  const cabinetCategories = [
    { id: 'cabinet', name: 'الخزانات عامة', keywords: 'wardrobe design empty closet interior' },
    { id: 'wardrobe', name: 'خزانات الملابس', keywords: 'empty wardrobe interior design bedroom closet' },
    { id: 'modern-cabinet', name: 'الخزانات العصرية', keywords: 'modern wardrobe design contemporary closet interior' },
    { id: 'classic-cabinet', name: 'الخزانات الكلاسيكية', keywords: 'classic wardrobe design traditional closet interior' },
    { id: 'luxury-cabinet', name: 'الخزانات الفاخرة', keywords: 'luxury wardrobe design expensive closet interior' },
    { id: 'built-in', name: 'الخزانات المدمجة', keywords: 'built-in wardrobe design fitted closet interior' }
  ]

  // جلب الصور من Unsplash
  const fetchCabinets = async (category = 'cabinet') => {
    try {
      setLoading(true)
      const accessKey = import.meta.env.VITE_UNSPLASH_ACCESS_KEY

      if (!accessKey) {
        console.error('Unsplash access key not found')
        return
      }

      const categoryData = cabinetCategories.find(cat => cat.id === category)
      const keywords = categoryData ? categoryData.keywords : 'wardrobe closet'

      const response = await fetch(
        `https://api.unsplash.com/search/photos?query=${encodeURIComponent(keywords)}&per_page=20&page=1&orientation=landscape`,
        {
          headers: {
            'Authorization': `Client-ID ${accessKey}`
          }
        }
      )

      const data = await response.json()

      if (data.results) {
        // تحويل بيانات Unsplash وتجميع الصور المتشابهة
        const groupedCabinets = {}

        data.results.forEach(photo => {
          // تجميع الصور حسب المصور أو الكلمات المتشابهة
          const groupKey = photo.user.username

          if (!groupedCabinets[groupKey]) {
            groupedCabinets[groupKey] = {
              id: photo.id,
              webformatURL: photo.urls.regular,
              largeImageURL: photo.urls.full,
              fullHDURL: photo.urls.raw,
              tags: photo.alt_description || photo.description || keywords,
              views: photo.views || Math.floor(Math.random() * 10000) + 1000,
              likes: photo.likes || Math.floor(Math.random() * 500) + 50,
              photographer: photo.user.name,
              photographer_url: photo.user.links.html,
              download_url: photo.links.download,
              images: [photo] // مصفوفة الصور المتعددة
            }
          } else {
            // إضافة صورة إضافية لنفس المجموعة
            groupedCabinets[groupKey].images.push(photo)
          }
        })

        // تحويل المجموعات إلى مصفوفة
        const formattedCabinets = Object.values(groupedCabinets)
        setCabinets(formattedCabinets)
      }
    } catch (error) {
      console.error('Error fetching cabinets:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCabinets(currentCategory)
  }, [currentCategory])

  const handleCategoryChange = (categoryId) => {
    setCurrentCategory(categoryId)
  }

  const openModal = (product) => {
    setSelectedProduct(product)
  }

  const closeModal = () => {
    setSelectedProduct(null)
  }

  return (
    <>
      <Helmet>
        <title>الخزانات - خبرة المطابخ</title>
        <meta name="description" content="تصفح مجموعتنا المتنوعة من الخزانات وخزانات الملابس العصرية والكلاسيكية. تصاميم خزانات مميزة تناسب جميع المساحات." />
        <meta name="keywords" content="خزانات, خزانات ملابس, خزانات عصرية, خزانات كلاسيكية, خزانات مدمجة, تصميم خزانات" />
      </Helmet>

      <div className="min-h-screen bg-gray-50 pt-20">
        {/* Header */}
        <div className="bg-white shadow-sm">
          <div className="container mx-auto px-6 py-8">
            <h1 className="text-4xl font-bold text-gray-900 text-center mb-4">
              مجموعة الخزانات
            </h1>
            <p className="text-xl text-gray-600 text-center max-w-3xl mx-auto">
              اكتشف تشكيلتنا المتنوعة من الخزانات التي تجمع بين الأناقة والتنظيم
            </p>
          </div>
        </div>

        {/* Categories Filter */}
        <div className="container mx-auto px-6 py-8">
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            {cabinetCategories.map((category) => (
              <button
                key={category.id}
                onClick={() => handleCategoryChange(category.id)}
                className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                  currentCategory === category.id
                    ? 'bg-primary text-white shadow-lg'
                    : 'bg-white text-gray-700 hover:bg-gray-100 shadow-md'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* Loading */}
          {loading && (
            <div className="flex justify-center items-center py-20">
              <div className="spinner w-12 h-12"></div>
              <span className="mr-4 text-lg">جاري تحميل الخزانات...</span>
            </div>
          )}

          {/* Products Grid */}
          {!loading && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {cabinets.map((cabinet) => (
                <div
                  key={cabinet.id}
                  className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer"
                  onClick={() => openModal(cabinet)}
                >
                  <div className="relative overflow-hidden">
                    <img
                      src={cabinet.webformatURL}
                      alt={cabinet.tags}
                      className="w-full h-64 object-cover transition-transform duration-300 hover:scale-110"
                      loading="lazy"
                    />
                    <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full">
                      <div className="flex items-center space-x-1 rtl:space-x-reverse">
                        <i className="ri-heart-line text-red-500"></i>
                        <span className="text-sm font-medium">{cabinet.likes}</span>
                      </div>
                    </div>

                    {/* مؤشر عدد الصور */}
                    {cabinet.images && cabinet.images.length > 1 && (
                      <div className="absolute top-4 left-4 bg-black/70 backdrop-blur-sm px-3 py-1 rounded-full">
                        <div className="flex items-center space-x-1 rtl:space-x-reverse">
                          <i className="ri-image-line text-white text-sm"></i>
                          <span className="text-sm font-medium text-white">{cabinet.images.length}</span>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-gray-900 mb-2 line-clamp-2">
                      تصميم خزانة {currentCategory === 'cabinet' ? 'عصرية' : cabinetCategories.find(cat => cat.id === currentCategory)?.name.replace('الخزانات ', '').replace('خزانات ', '') || 'مميزة'}
                    </h3>
                    <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                      {cabinet.tags}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-500">
                        <i className="ri-eye-line"></i>
                        <span>{cabinet.views.toLocaleString()}</span>
                      </div>
                      
                      <button className="bg-primary text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-600 transition-colors">
                        عرض التفاصيل
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Empty State */}
          {!loading && cabinets.length === 0 && (
            <div className="text-center py-20">
              <i className="ri-image-line text-6xl text-gray-400 mb-4"></i>
              <h3 className="text-xl font-bold text-gray-900 mb-2">لا توجد خزانات</h3>
              <p className="text-gray-600">جرب تغيير الفئة أو تحقق من اتصال الإنترنت</p>
            </div>
          )}
        </div>
      </div>

      {/* Product Modal */}
      {selectedProduct && (
        <ProductModal
          product={selectedProduct}
          onClose={closeModal}
          type="cabinet"
        />
      )}
    </>
  )
}

export default CabinetsPage
