import { useRef, useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, EffectCoverflow, Autoplay, Thumbs } from 'swiper/modules';
import { motion, useInView, AnimatePresence } from 'framer-motion';
import { getKitchensData, getFooterData } from '../../database/api-client.js';
import { getImageURL } from '../config/api.js';
import Navbar from '../components/Navbar';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/effect-coverflow';
import 'swiper/css/thumbs';

const KitchensPage = () => {
  const sectionRef = useRef(null);
  const [thumbsSwiper, setThumbsSwiper] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [lightboxImage, setLightboxImage] = useState(null);
  const [modalImageIndex, setModalImageIndex] = useState(0);
  const isInView = useInView(sectionRef, { once: true, threshold: 0.1 });

  const [kitchens, setKitchens] = useState([]);
  const [whatsappNumber, setWhatsappNumber] = useState('');

  // قراءة البيانات من قاعدة البيانات عند تحميل المكون
  useEffect(() => {
    const loadKitchensData = async () => {
      try {
        const kitchensData = await getKitchensData();
        console.log('Loaded kitchens data:', kitchensData);
        if (kitchensData && kitchensData.length > 0) {
          // تحويل البيانات من قاعدة البيانات إلى التنسيق المطلوب
          const formattedKitchens = kitchensData.map(kitchen => ({
            id: kitchen.id,
            title: kitchen.title,
            description: kitchen.description,
            // إذا لم تكن هناك فئة محددة، استخدم 'modern-kitchens' كافتراضي
            category: kitchen.category_slug || 'modern-kitchens',
            images: kitchen.images?.map(img => {
              const imageUrl = typeof img === 'string' ? img : (img.image_url || img);
              return getImageURL(imageUrl);
            }) || []
          }));
          console.log('Formatted kitchens:', formattedKitchens);
          setKitchens(formattedKitchens);
        } else {
          setKitchens([]);
        }
      } catch (error) {
        console.error('Error loading kitchens data:', error);
        setKitchens([]);
      }
    };

    const loadWhatsappNumber = async () => {
      try {
        const footerData = await getFooterData();
        if (footerData && footerData.whatsapp) {
          setWhatsappNumber(footerData.whatsapp);
        }
      } catch (error) {
        console.error('Error loading WhatsApp number:', error);
      }
    };

    loadKitchensData();
    loadWhatsappNumber();
  }, []);

  // فئات المطابخ
  const categories = [
    { id: 'all', name: 'جميع المطابخ', icon: 'ri-apps-line' },
    { id: 'modern-kitchens', name: 'عصري', icon: 'ri-building-line' },
    { id: 'classic-kitchens', name: 'كلاسيكي', icon: 'ri-home-heart-line' },
    { id: 'luxury-kitchens', name: 'فاخر', icon: 'ri-vip-crown-line' }
  ];

  const filteredKitchens = selectedCategory === 'all'
    ? kitchens
    : kitchens.filter(kitchen => kitchen.category === selectedCategory);

  // دالة لفتح WhatsApp مع رابط المنتج
  const openWhatsApp = (kitchen) => {
    const currentUrl = window.location.href;
    const productUrl = `${currentUrl}#kitchen-${kitchen.id}`;
    const message = `مرحباً، أريد الاستفسار عن هذا المطبخ: ${kitchen.title}\n${productUrl}`;

    let phoneNumber = whatsappNumber;
    if (phoneNumber.startsWith('0')) {
      phoneNumber = '966' + phoneNumber.substring(1);
    }

    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <>
      <Helmet>
        <title>معرض المطابخ - خبرة المطابخ</title>
        <meta name="description" content="استكشف مجموعتنا المتنوعة من تصاميم المطابخ العصرية والكلاسيكية والفاخرة" />
      </Helmet>

      <Navbar />

      <section className="relative py-24 bg-gradient-to-br from-slate-50 via-white to-blue-50 overflow-hidden" ref={sectionRef}>
        {/* Background Decorations */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-200/20 to-purple-200/20 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-pink-200/20 to-orange-200/20 rounded-full blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          {/* Header */}
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
          >
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl mb-6 shadow-lg">
              <i className="ri-restaurant-line text-2xl text-white"></i>
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 bg-clip-text text-transparent mb-6">
              معرض
              <span className="block bg-gradient-to-r from-orange-600 via-red-600 to-pink-600 bg-clip-text text-transparent">
                المطابخ الفاخرة
              </span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              استكشف مجموعتنا الكاملة من تصاميم المطابخ العصرية والكلاسيكية التي تناسب مختلف الأذواق والمساحات
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto mt-8 rounded-full"></div>
          </motion.div>

          {/* Category Filter */}
          <motion.div
            className="flex flex-wrap justify-center gap-4 mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`flex items-center space-x-2 rtl:space-x-reverse px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                  selectedCategory === category.id
                    ? 'bg-gradient-to-r from-orange-600 to-red-600 text-white shadow-lg transform scale-105'
                    : 'bg-white text-gray-700 hover:bg-gray-50 shadow-md hover:shadow-lg'
                }`}
              >
                <i className={`${category.icon} text-lg`}></i>
                <span>{category.name}</span>
              </button>
            ))}
          </motion.div>

          {/* Kitchens Grid */}
          {filteredKitchens.length > 0 ? (
            <motion.div
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              {filteredKitchens.map((kitchen, index) => (
                <motion.div
                  key={kitchen.id}
                  className="group relative bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 cursor-pointer"
                  whileHover={{ y: -10 }}
                  onClick={() => setLightboxImage(kitchen)}
                  initial={{ opacity: 0, y: 30 }}
                  animate={isInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <div className="relative h-80 md:h-96 overflow-hidden">
                    <img
                      src={kitchen.images[0]}
                      alt={kitchen.title}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm text-gray-800 px-3 py-1 rounded-full text-xs font-medium">
                      {categories.find(cat => cat.id === kitchen.category)?.name || 'عصري'}
                    </div>
                    <div className="absolute bottom-4 left-4 right-4 text-white transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500">
                      <h3 className="text-xl font-bold mb-2">{kitchen.title}</h3>
                      <p className="text-sm text-gray-200 line-clamp-2">{kitchen.description}</p>
                    </div>
                  </div>

                  <div className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-500">
                        <i className="ri-image-line"></i>
                        <span className="text-sm">{kitchen.images.length} صورة</span>
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          openWhatsApp(kitchen);
                        }}
                        className="flex items-center space-x-2 rtl:space-x-reverse bg-green-500 text-white px-4 py-2 rounded-full hover:bg-green-600 transition-colors duration-300"
                      >
                        <i className="ri-whatsapp-line"></i>
                        <span className="text-sm">استفسار</span>
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          ) : (
            <motion.div
              className="text-center py-16"
              initial={{ opacity: 0 }}
              animate={isInView ? { opacity: 1 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className="ri-restaurant-line text-3xl text-gray-400"></i>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">لا توجد مطابخ</h3>
              <p className="text-gray-600 mb-6">لم يتم العثور على مطابخ في هذه الفئة</p>
            </motion.div>
          )}
        </div>
      </section>

      {/* Lightbox Modal */}
      <AnimatePresence>
        {lightboxImage && (
          <motion.div
            className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setLightboxImage(null)}
          >
            <motion.div
              className="relative max-w-7xl w-full max-h-[90vh] bg-white rounded-2xl overflow-hidden"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Close Button */}
              <button
                onClick={() => setLightboxImage(null)}
                className="absolute top-4 right-4 z-10 w-10 h-10 bg-black/50 text-white rounded-full flex items-center justify-center hover:bg-black/70 transition-colors duration-300"
              >
                <i className="ri-close-line text-xl"></i>
              </button>

              <div className="flex flex-col md:flex-row h-full">
                {/* Image Section */}
                <div className="flex-1 relative bg-gray-100">
                  {/* Mobile Image Slider */}
                  <div className="md:hidden h-80">
                    <Swiper
                      modules={[Navigation, Pagination]}
                      navigation
                      pagination={{ clickable: true }}
                      className="h-full"
                      onSlideChange={(swiper) => setModalImageIndex(swiper.activeIndex)}
                    >
                      {lightboxImage.images.map((image, index) => (
                        <SwiperSlide key={index}>
                          <div className="relative h-full">
                            <img
                              src={image}
                              alt={`${lightboxImage.title} - صورة ${index + 1}`}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        </SwiperSlide>
                      ))}
                    </Swiper>
                  </div>

                  {/* Desktop Image Display */}
                  <div className="hidden md:block h-96 lg:h-[500px]">
                    <img
                      src={lightboxImage.images[modalImageIndex]}
                      alt={`${lightboxImage.title} - صورة ${modalImageIndex + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </div>

                  {/* Image Navigation for Desktop */}
                  {lightboxImage.images.length > 1 && (
                    <div className="hidden md:flex absolute bottom-4 left-1/2 transform -translate-x-1/2 space-x-2 rtl:space-x-reverse">
                      {lightboxImage.images.map((_, index) => (
                        <button
                          key={index}
                          onClick={() => setModalImageIndex(index)}
                          className={`w-3 h-3 rounded-full transition-colors duration-300 ${
                            index === modalImageIndex ? 'bg-white' : 'bg-white/50'
                          }`}
                        />
                      ))}
                    </div>
                  )}
                </div>

                {/* Content Section */}
                <div className="w-full md:w-96 p-6 md:p-8 flex flex-col">
                  <div className="flex-1">
                    <div className="mb-4">
                      <span className="inline-block bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium mb-3">
                        {categories.find(cat => cat.id === lightboxImage.category)?.name || 'عصري'}
                      </span>
                      <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-3">
                        {lightboxImage.title}
                      </h2>
                      <p className="text-gray-600 leading-relaxed">
                        {lightboxImage.description}
                      </p>
                    </div>

                    <div className="border-t pt-6 mb-6">
                      <h3 className="text-lg font-semibold text-gray-800 mb-4">تفاصيل المطبخ</h3>
                      <div className="space-y-3">
                        <div className="flex items-center space-x-3 rtl:space-x-reverse">
                          <i className="ri-image-line text-orange-600"></i>
                          <span className="text-gray-600">{lightboxImage.images.length} صورة متاحة</span>
                        </div>
                        <div className="flex items-center space-x-3 rtl:space-x-reverse">
                          <i className="ri-palette-line text-orange-600"></i>
                          <span className="text-gray-600">تصميم {categories.find(cat => cat.id === lightboxImage.category)?.name || 'عصري'}</span>
                        </div>
                        <div className="flex items-center space-x-3 rtl:space-x-reverse">
                          <i className="ri-tools-line text-orange-600"></i>
                          <span className="text-gray-600">تنفيذ احترافي</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Social Media & Contact */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">تواصل معنا</h3>

                    {/* Mobile Social Media */}
                    <div className="md:hidden">
                      <div className="flex justify-center gap-3 mb-6">
                        <motion.a
                          href="https://x.com/expertwonders"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center justify-center w-12 h-12 bg-black text-white rounded-full hover:bg-gray-800 transition-colors duration-300"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <i className="ri-twitter-x-line text-lg"></i>
                        </motion.a>

                        <motion.a
                          href="https://snapchat.com/add/expertwonders"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center justify-center w-12 h-12 bg-yellow-500 text-white rounded-full hover:bg-yellow-600 transition-colors duration-300"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <i className="ri-snapchat-line text-lg"></i>
                        </motion.a>

                        <motion.a
                          href="https://instagram.com/expertwonders"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full hover:from-purple-600 hover:to-pink-600 transition-colors duration-300"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <i className="ri-instagram-line text-lg"></i>
                        </motion.a>

                        <motion.button
                          onClick={() => openWhatsApp(lightboxImage)}
                          className="flex items-center justify-center w-12 h-12 bg-green-500 text-white rounded-full hover:bg-green-600 transition-colors duration-300"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <i className="ri-whatsapp-line text-lg"></i>
                        </motion.button>

                        <motion.a
                          href="https://tiktok.com/@expertwonders"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center justify-center w-12 h-12 bg-black text-white rounded-full hover:bg-gray-800 transition-colors duration-300"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <i className="ri-tiktok-line text-lg"></i>
                        </motion.a>
                      </div>
                    </div>

                    {/* Desktop Social Media */}
                    <div className="hidden md:block">
                      <div className="flex justify-center gap-4 mb-8">
                        <motion.a
                          href="https://x.com/expertwonders"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center justify-center w-16 h-16 bg-black text-white rounded-full hover:bg-gray-800 transition-colors duration-300 shadow-lg"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <i className="ri-twitter-x-line text-2xl"></i>
                        </motion.a>

                        <motion.a
                          href="https://snapchat.com/add/expertwonders"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center justify-center w-16 h-16 bg-yellow-500 text-white rounded-full hover:bg-yellow-600 transition-colors duration-300 shadow-lg"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <i className="ri-snapchat-line text-2xl"></i>
                        </motion.a>

                        <motion.a
                          href="https://instagram.com/expertwonders"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full hover:from-purple-600 hover:to-pink-600 transition-colors duration-300 shadow-lg"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <i className="ri-instagram-line text-2xl"></i>
                        </motion.a>

                        <motion.button
                          onClick={() => openWhatsApp(lightboxImage)}
                          className="flex items-center justify-center w-16 h-16 bg-green-500 text-white rounded-full hover:bg-green-600 transition-colors duration-300 shadow-lg"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <i className="ri-whatsapp-line text-2xl"></i>
                        </motion.button>

                        <motion.a
                          href="https://tiktok.com/@expertwonders"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center justify-center w-16 h-16 bg-black text-white rounded-full hover:bg-gray-800 transition-colors duration-300 shadow-lg"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <i className="ri-tiktok-line text-2xl"></i>
                        </motion.a>
                      </div>
                    </div>

                    <motion.button
                      onClick={() => openWhatsApp(lightboxImage)}
                      className="w-full bg-gradient-to-r from-green-600 to-green-700 text-white py-4 px-6 rounded-xl font-semibold hover:from-green-700 hover:to-green-800 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center space-x-3 rtl:space-x-reverse"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <i className="ri-whatsapp-line text-xl"></i>
                      <span>استفسار عبر واتساب</span>
                    </motion.button>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default KitchensPage;