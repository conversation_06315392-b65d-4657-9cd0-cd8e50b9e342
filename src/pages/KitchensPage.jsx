import { useState, useEffect } from 'react'
import { Helmet } from 'react-helmet-async'
import ProductModal from '../components/ProductModal'

const KitchensPage = () => {
  const [kitchens, setKitchens] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedProduct, setSelectedProduct] = useState(null)
  const [currentCategory, setCurrentCategory] = useState('kitchen')

  // الكلمات المفتاحية للمطابخ
  const kitchenCategories = [
    { id: 'kitchen', name: 'المطابخ عامة', keywords: 'modern kitchen' },
    { id: 'modern', name: 'المطابخ العصرية', keywords: 'modern kitchen design' },
    { id: 'classic', name: 'المطابخ الكلاسيكية', keywords: 'classic kitchen interior' },
    { id: 'luxury', name: 'المطابخ الفاخرة', keywords: 'luxury kitchen design' },
    { id: 'scandinavian', name: 'المطابخ الإسكندنافية', keywords: 'scandinavian kitchen' },
    { id: 'minimal', name: 'المطابخ المينيمال', keywords: 'minimalist kitchen' }
  ]

  // جلب الصور من Unsplash
  const fetchKitchens = async (category = 'kitchen') => {
    try {
      setLoading(true)
      const accessKey = import.meta.env.VITE_UNSPLASH_ACCESS_KEY

      if (!accessKey) {
        console.error('Unsplash access key not found')
        return
      }

      const categoryData = kitchenCategories.find(cat => cat.id === category)
      const keywords = categoryData ? categoryData.keywords : 'modern kitchen'

      const response = await fetch(
        `https://api.unsplash.com/search/photos?query=${encodeURIComponent(keywords)}&per_page=20&page=1&orientation=landscape`,
        {
          headers: {
            'Authorization': `Client-ID ${accessKey}`
          }
        }
      )

      const data = await response.json()

      if (data.results) {
        // تحويل بيانات Unsplash وتجميع الصور المتشابهة
        const groupedKitchens = {}

        data.results.forEach(photo => {
          // تجميع الصور حسب المصور أو الكلمات المتشابهة
          const groupKey = photo.user.username

          if (!groupedKitchens[groupKey]) {
            groupedKitchens[groupKey] = {
              id: photo.id,
              webformatURL: photo.urls.regular,
              largeImageURL: photo.urls.full,
              fullHDURL: photo.urls.raw,
              tags: photo.alt_description || photo.description || keywords,
              views: photo.views || Math.floor(Math.random() * 10000) + 1000,
              likes: photo.likes || Math.floor(Math.random() * 500) + 50,
              photographer: photo.user.name,
              photographer_url: photo.user.links.html,
              download_url: photo.links.download,
              images: [photo] // مصفوفة الصور المتعددة
            }
          } else {
            // إضافة صورة إضافية لنفس المجموعة
            groupedKitchens[groupKey].images.push(photo)
          }
        })

        // تحويل المجموعات إلى مصفوفة
        const formattedKitchens = Object.values(groupedKitchens)
        setKitchens(formattedKitchens)
      }
    } catch (error) {
      console.error('Error fetching kitchens:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchKitchens(currentCategory)
  }, [currentCategory])

  const handleCategoryChange = (categoryId) => {
    setCurrentCategory(categoryId)
  }

  const openModal = (product) => {
    setSelectedProduct(product)
  }

  const closeModal = () => {
    setSelectedProduct(null)
  }

  return (
    <>
      <Helmet>
        <title>المطابخ - خبرة المطابخ</title>
        <meta name="description" content="تصفح مجموعتنا المتنوعة من المطابخ العصرية والكلاسيكية والفاخرة. تصاميم مطابخ مميزة تناسب جميع الأذواق والمساحات." />
        <meta name="keywords" content="مطابخ, مطابخ عصرية, مطابخ كلاسيكية, مطابخ فاخرة, تصميم مطابخ, مطابخ مينيمال" />
      </Helmet>

      <div className="min-h-screen bg-gray-50 pt-20">
        {/* Header */}
        <div className="bg-white shadow-sm">
          <div className="container mx-auto px-6 py-8">
            <h1 className="text-4xl font-bold text-gray-900 text-center mb-4">
              مجموعة المطابخ
            </h1>
            <p className="text-xl text-gray-600 text-center max-w-3xl mx-auto">
              اكتشف تشكيلتنا المتنوعة من المطابخ التي تجمع بين الجمال والوظيفة
            </p>
          </div>
        </div>

        {/* Categories Filter */}
        <div className="container mx-auto px-6 py-8">
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            {kitchenCategories.map((category) => (
              <button
                key={category.id}
                onClick={() => handleCategoryChange(category.id)}
                className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                  currentCategory === category.id
                    ? 'bg-primary text-white shadow-lg'
                    : 'bg-white text-gray-700 hover:bg-gray-100 shadow-md'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* Loading */}
          {loading && (
            <div className="flex justify-center items-center py-20">
              <div className="spinner w-12 h-12"></div>
              <span className="mr-4 text-lg">جاري تحميل المطابخ...</span>
            </div>
          )}

          {/* Products Grid */}
          {!loading && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {kitchens.map((kitchen) => (
                <div
                  key={kitchen.id}
                  className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer"
                  onClick={() => openModal(kitchen)}
                >
                  <div className="relative overflow-hidden">
                    <img
                      src={kitchen.webformatURL}
                      alt={kitchen.tags}
                      className="w-full h-64 object-cover transition-transform duration-300 hover:scale-110"
                      loading="lazy"
                    />
                    <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full">
                      <div className="flex items-center space-x-1 rtl:space-x-reverse">
                        <i className="ri-heart-line text-red-500"></i>
                        <span className="text-sm font-medium">{kitchen.likes}</span>
                      </div>
                    </div>

                    {/* مؤشر عدد الصور */}
                    {kitchen.images && kitchen.images.length > 1 && (
                      <div className="absolute top-4 left-4 bg-black/70 backdrop-blur-sm px-3 py-1 rounded-full">
                        <div className="flex items-center space-x-1 rtl:space-x-reverse">
                          <i className="ri-image-line text-white text-sm"></i>
                          <span className="text-sm font-medium text-white">{kitchen.images.length}</span>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-gray-900 mb-2 line-clamp-2">
                      تصميم مطبخ {currentCategory === 'kitchen' ? 'عصري' : kitchenCategories.find(cat => cat.id === currentCategory)?.name.replace('المطابخ ', '') || 'مميز'}
                    </h3>
                    <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                      {kitchen.tags}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-500">
                        <i className="ri-eye-line"></i>
                        <span>{kitchen.views.toLocaleString()}</span>
                      </div>
                      
                      <button className="bg-primary text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-600 transition-colors">
                        عرض التفاصيل
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Empty State */}
          {!loading && kitchens.length === 0 && (
            <div className="text-center py-20">
              <i className="ri-image-line text-6xl text-gray-400 mb-4"></i>
              <h3 className="text-xl font-bold text-gray-900 mb-2">لا توجد مطابخ</h3>
              <p className="text-gray-600">جرب تغيير الفئة أو تحقق من اتصال الإنترنت</p>
            </div>
          )}
        </div>
      </div>

      {/* Product Modal */}
      {selectedProduct && (
        <ProductModal
          product={selectedProduct}
          onClose={closeModal}
          type="kitchen"
        />
      )}
    </>
  )
}

export default KitchensPage
