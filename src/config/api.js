// API Configuration
// إعدادات API للبيئات المختلفة

const getApiConfig = () => {
  // تحديد البيئة الحالية
  const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
  const isProduction = window.location.hostname === 'khobrakitchens.com';

  if (isDevelopment) {
    return {
      baseURL: 'http://localhost:3002',
      uploadsURL: 'http://localhost:3002'
    };
  } else if (isProduction) {
    return {
      baseURL: 'https://khobrakitchens.com/api',
      uploadsURL: 'https://khobrakitchens.com'
    };
  } else {
    // افتراضي للبيئات الأخرى
    return {
      baseURL: `${window.location.protocol}//${window.location.host}/api`,
      uploadsURL: `${window.location.protocol}//${window.location.host}`
    };
  }
};

export const API_CONFIG = getApiConfig();

// دالة مساعدة لبناء URL كامل للصور
export const getImageURL = (imagePath) => {
  if (!imagePath) return null;
  
  // إذا كان الرابط كاملاً بالفعل، أرجعه كما هو
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }
  
  // إذا كان مسار نسبي، أضف base URL
  return `${API_CONFIG.uploadsURL}${imagePath}`;
};

// دالة مساعدة لبناء URL للـ API
export const getApiURL = (endpoint) => {
  return `${API_CONFIG.baseURL}${endpoint}`;
};
